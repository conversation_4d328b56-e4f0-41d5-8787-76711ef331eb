#!/bin/bash
set -e

# Update and install necessary packages
apt-get update
apt-get install -y openssh-server sudo libpam-pwquality

# Create base directories
mkdir -p /data
chmod 755 /data
chown root:root /data

mkdir -p /upload
chmod 755 /upload
chown root:root /upload

# Run SFTP server setup script
bash setup_sftp_server.sh

# Check if backup file is provided
if [ $# -gt 0 ]; then
    BACKUP_FILE=$1
    shift  # Remove first argument (backup file)
    
    echo "Restoring from backup: $BACKUP_FILE"
    if [ $# -gt 0 ]; then
        echo "Users to skip: $@"
    else
        echo "No users to skip specified"
    fi
    
    # Pass all remaining arguments (or none) to restore script
    bash secure-gcp-docker-sftp-restore-script-preserve-creds.sh "$BACKUP_FILE" $@
else
    echo "No backup file provided. SFTP server is set up without restoring data."
fi

echo "SFTP server setup completed."