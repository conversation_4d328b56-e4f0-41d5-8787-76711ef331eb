#!/bin/bash
set -e

# Update package lists and install necessary packages
apt-get update
apt-get install -y openssh-server sudo libpam-pwquality

# Configure SSH server
sed -i 's/#PermitRootLogin prohibit-password/PermitRootLogin no/' /etc/ssh/sshd_config
sed 's@session\s*required\s*pam_loginuid.so@session optional pam_loginuid.so@g' -i /etc/pam.d/sshd

# Backup original sshd_config
cp /etc/ssh/sshd_config /etc/ssh/sshd_config.bak

# Remove all existing SFTP and Match Group configurations
sed -i '/^Subsystem.*sftp/d' /etc/ssh/sshd_config
sed -i '/^Match Group.*/,+10d' /etc/ssh/sshd_config
sed -i '/^# SFTP configuration/d' /etc/ssh/sshd_config
sed -i '/^# SFTP group configuration/d' /etc/ssh/sshd_config

# Configure SSH authentication methods (remove existing ones first)
sed -i '/^PasswordAuthentication/d' /etc/ssh/sshd_config
sed -i '/^PubkeyAuthentication/d' /etc/ssh/sshd_config
sed -i '/^PermitEmptyPasswords/d' /etc/ssh/sshd_config
sed -i '/^AuthorizedKeysFile/d' /etc/ssh/sshd_config

# Add fresh SSH configuration
cat << EOF >> /etc/ssh/sshd_config

# SSH authentication configuration
PasswordAuthentication yes
PubkeyAuthentication yes
PermitEmptyPasswords no
AuthorizedKeysFile .ssh/authorized_keys

# SFTP configuration
Subsystem sftp internal-sftp

# SFTP group configuration
Match Group sftp_users
    ChrootDirectory /upload/%u
    ForceCommand internal-sftp
    AllowTcpForwarding no
    X11Forwarding no
EOF

# Set password quality requirements
sed -i 's/password\s*requisite\s*pam_pwquality.so.*/password requisite pam_pwquality.so retry=3 minlen=14 difok=3 ucredit=-1 lcredit=-1 dcredit=-1 ocredit=-1/' /etc/pam.d/common-password

# Create and configure base directories
mkdir -p /data
chmod 755 /data
chown root:root /data

mkdir -p /upload
chmod 755 /upload
chown root:root /upload

# Create privilege separation directory if it doesn't exist
mkdir -p /run/sshd
chmod 0755 /run/sshd

# Create SFTP group (ignore error if it already exists)
groupadd sftp_users || true

# Restart SSH service
systemctl restart ssh

echo "SFTP server setup completed."
echo "Original sshd_config backed up to /etc/ssh/sshd_config.bak"