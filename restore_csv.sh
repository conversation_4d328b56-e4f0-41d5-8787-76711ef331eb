#!/bin/bash

# Check if script is run as root
if [[ $EUID -ne 0 ]]; then
   echo "This script must be run as root"
   exit 1
fi

# Check if CSV file was provided
if [ $# -ne 1 ]; then
    echo "Usage: $0 <csv_file>"
    echo "Example: $0 users.csv"
    echo "CSV format should be: username,password"
    exit 1
fi

CSV_FILE=$1
SFTP_GROUP="sftp_users"

# Check if CSV file exists
if [ ! -f "$CSV_FILE" ]; then
    echo "Error: CSV file '$CSV_FILE' not found"
    exit 1
fi

# Check if required directories exist
if [ ! -d "/upload" ]; then
    echo "Creating /upload directory..."
    mkdir -p /upload
    chmod 755 /upload
fi

# Check if SFTP group exists
if ! getent group "$SFTP_GROUP" >/dev/null; then
    echo "Creating SFTP users group..."
    groupadd "$SFTP_GROUP"
fi

# Function to validate username
validate_username() {
    local username=$1
    if ! [[ "$username" =~ ^[a-zA-Z][a-zA-Z0-9_-]*$ ]]; then
        echo "Invalid username format: $username"
        return 1
    fi
    return 0
}

# Function to verify a command succeeded
verify_command() {
    if [ $? -ne 0 ]; then
        echo "Error: $1"
        return 1
    fi
    return 0
}

# Function to set up a single user
setup_user() {
    local USERNAME=$1
    local PASSWORD=$2

    echo "Processing user: $USERNAME"

    # Check if user already exists
    if id "$USERNAME" &>/dev/null; then
        echo "User $USERNAME already exists, skipping..."
        return 0
    fi

    # Create user and add to sftp_users group
    useradd -g $SFTP_GROUP -d /upload -s /sbin/nologin "$USERNAME"
    if ! verify_command "Failed to create user $USERNAME"; then
        return 1
    fi

    # Set password
    echo "$USERNAME:$PASSWORD" | chpasswd
    if ! verify_command "Failed to set password for $USERNAME"; then
        return 1
    fi

    # Create the directory structure
    echo "Creating directory structure for $USERNAME..."
    mkdir -p "/data/$USERNAME/upload"
    if ! verify_command "Failed to create directories for $USERNAME"; then
        return 1
    fi

    # Set ownership and permissions
    echo "Setting up permissions..."
    chown root:$SFTP_GROUP "/data/$USERNAME"
    if ! verify_command "Failed to set ownership on /data/$USERNAME"; then
        return 1
    fi

    chmod 755 "/data/$USERNAME"
    if ! verify_command "Failed to set permissions on /data/$USERNAME"; then
        return 1
    fi

    chown "$USERNAME:$SFTP_GROUP" "/data/$USERNAME/upload"
    if ! verify_command "Failed to set ownership on /data/$USERNAME/upload"; then
        return 1
    fi

    chmod 775 "/data/$USERNAME/upload"
    if ! verify_command "Failed to set permissions on /data/$USERNAME/upload"; then
        return 1
    fi

    # Create symbolic link
    echo "Creating symbolic link..."
    ln -sfn "/data/$USERNAME" "/upload/$USERNAME"
    if ! verify_command "Failed to create symbolic link"; then
        return 1
    fi

    # Verify the setup
    echo "Verifying setup for $USERNAME..."
    echo "Checking user and group:"
    id "$USERNAME"
    getent group "$SFTP_GROUP"

    echo -e "\nChecking directory structure:"
    ls -la "/data/$USERNAME"
    ls -la "/data/$USERNAME/upload"

    echo -e "\nChecking symbolic link:"
    ls -la "/upload/$USERNAME"
    namei -l "/upload/$USERNAME"

    echo -e "\nSetup complete for user $USERNAME"
    return 0
}

# Process the CSV file
echo "Starting user setup from CSV file: $CSV_FILE"
echo "----------------------------------------"

# Use a different approach to read the CSV to avoid subshell issues
while IFS=, read -r username password
do
    # Skip the header line
    if [[ "$username" == "username" ]]; then
        continue
    fi

    # Remove any whitespace or quotes
    username=$(echo "$username" | tr -d '"' | tr -d ' ')
    password=$(echo "$password" | tr -d '"' | tr -d ' ')
    
    # Validate username and password
    if [ -z "$username" ] || [ -z "$password" ]; then
        echo "Error: Invalid line in CSV (empty username or password)"
        continue
    fi

    # Validate username format
    if ! validate_username "$username"; then
        echo "Skipping invalid username: $username"
        continue
    fi

    # Set up the user
    setup_user "$username" "$password"
    echo "----------------------------------------"
done < "$CSV_FILE"

echo "CSV processing complete"