#!/bin/bash

# Set the backup directory
BACKUP_DIR="/tmp/sftp_backup"
BACKUP_FILENAME="sftp_backup_$(date +%Y%m%d).tar.gz"

# Create backup directory
mkdir -p $BACKUP_DIR

# Ensure proper permissions on the backup directory
chmod 700 $BA<PERSON><PERSON>UP_DIR

# Backup folder structure (excluding file contents)
find /home -type d -print0 | tar -cvzf $BACKUP_DIR/folder_structure.tar.gz --null -T -

# Backup SFTP user information (including encrypted passwords)
# Use sudo to ensure we have proper permissions
cp -p /etc/passwd $BACKUP_DIR/passwd
cp -p /etc/shadow $BACKUP_DIR/shadow
cp -p /etc/group $BACKUP_DIR/group

# Ensure proper permissions on the copied files
chmod 600 $BACKUP_DIR/shadow
chmod 644 $BACKUP_DIR/passwd $BACKUP_DIR/group

# Backup folder permissions and ownership
find /home -type d -print0 | xargs -0 stat --format="%n,%a,%U,%G" > $BACKUP_DIR/folder_permissions.csv

# Backup SFTP user data (adjust the path if your SFTP root is different)
tar -czf $BACKUP_DIR/sftp_user_data.tar.gz /home

# Backup SSH configuration
cp /etc/ssh/sshd_config $BACKUP_DIR/sshd_config

# Compress the backup
tar -czf $BACKUP_FILENAME $BACKUP_DIR

# Set proper permissions on the final backup file
chmod 600 $BACKUP_FILENAME

# Clean up
rm -rf $BACKUP_DIR

echo "Backup completed: $BACKUP_FILENAME"
echo "Please ensure to keep $BACKUP_FILENAME secure as it contains sensitive information."