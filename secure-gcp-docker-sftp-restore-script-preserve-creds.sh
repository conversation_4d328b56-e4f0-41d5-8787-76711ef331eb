#!/bin/bash

set -e
set -x  # Enable debugging output

# Check if a backup file is provided as an argument
if [ $# -lt 1 ]; then
    echo "Usage: $0 <backup_file> [users_to_skip...]"
    echo "Example: $0 sftp_backup.tar.gz user1 user2 user3"
    exit 1
fi

BACKUP_FILE=$1
shift  # Remove backup file from arguments
USERS_TO_SKIP=$@  # Remaining arguments are users to skip (might be empty)
EXTRACT_DIR="/tmp/sftp_restore"
SFTP_GROUP="sftp_users"

# Check if backup file exists
if [ ! -f "$BACKUP_FILE" ]; then
    echo "Backup file not found: $BACKUP_FILE"
    exit 1
fi

# Extract the main backup
rm -rf $EXTRACT_DIR
mkdir -p $EXTRACT_DIR
tar -xzf $BACKUP_FILE -C $EXTRACT_DIR

BACKUP_CONTENTS="$EXTRACT_DIR/tmp/sftp_backup"

# Create SFTP group if it doesn't exist
groupadd -f sftp_users

# Ensure base directories exist
mkdir -p /data
chmod 755 /data
chown root:root /data

mkdir -p /upload
chmod 755 /upload
chown root:root /upload

# Process all users from backup passwd file
echo "Processing users from backup..."
while IFS=: read -r username password uid gid info home shell; do
    # Skip if this user is in the skip list
    if [ -n "$USERS_TO_SKIP" ] && [[ " ${USERS_TO_SKIP[@]} " =~ " ${username} " ]]; then
        echo "Skipping user $username as requested"
        continue
    fi

    # Skip system users
    if [ "$uid" -lt 1000 ]; then
        continue
    fi

    # Only process SFTP users (checking for /upload in home directory)
    if [[ "$home" == "/upload" ]]; then
        echo "Processing SFTP user: $username"
        
        if id "$username" >/dev/null 2>&1; then
            echo "Updating existing user $username"
            usermod -g sftp_users -d /upload -s /sbin/nologin "$username" 2>/dev/null || true
        else
            echo "Creating new user $username"
            useradd -g sftp_users -d /upload -s /sbin/nologin "$username" || {
                echo "Failed to create user $username"
                continue
            }
        fi

        # Create directory structure
        echo "Setting up directory structure for $username"
        mkdir -p "/data/$username/upload"
        
        # Set correct ownership and permissions
        chown -R root:sftp_users "/data/$username"
        chown -R "$username:sftp_users" "/data/$username/upload"
        chmod 755 "/data/$username"
        chmod 775 "/data/$username/upload"

        # Create symbolic link in /upload for chroot
        ln -sfn "/data/$username" "/upload/$username"
    fi
done < "$BACKUP_CONTENTS/passwd"

# Restore shadow entries for the SFTP users we've added/updated
echo "Restoring password hashes..."
while IFS=: read -r username hash restofline; do
    if id "$username" >/dev/null 2>&1; then
        # Verify this is an SFTP user
        if getent passwd "$username" | grep -q "/upload"; then
            if [ -z "$USERS_TO_SKIP" ] || ! [[ " ${USERS_TO_SKIP[@]} " =~ " ${username} " ]]; then
                # Update using full line to preserve all fields
                escaped_username=$(echo "$username" | sed 's/[\/&]/\\&/g')
                escaped_hash=$(echo "$hash" | sed 's/[\/&]/\\&/g')
                sed -i "s/^$escaped_username:[^:]*/$escaped_username:$escaped_hash/" /etc/shadow || true
            fi
        fi
    fi
done < "$BACKUP_CONTENTS/shadow"

# Restore user data if it exists
echo "Restoring user data..."
if [ -f "$BACKUP_CONTENTS/sftp_user_data.tar.gz" ]; then
    cd / && tar -xzf "$BACKUP_CONTENTS/sftp_user_data.tar.gz" --strip-components=1 data/ 2>/dev/null || true
fi

# Verify permissions after restore
echo "Verifying permissions..."
# Base directories
chown root:root /data
chmod 755 /data
chown root:root /upload
chmod 755 /upload

# Fix permissions for all user directories
for dir in /data/*/; do
    if [ -d "$dir" ]; then
        username=$(basename "$dir")
        echo "Fixing permissions for $username"
        # Fix base directory permissions (root owned)
        chown root:sftp_users "$dir"
        chmod 755 "$dir"
        
        # Fix upload directory permissions (user owned)
        if [ -d "$dir/upload" ]; then
            chown -R "$username:sftp_users" "$dir/upload"
            chmod 775 "$dir/upload"
        fi

        # Ensure symbolic link exists and is correct
        ln -sfn "$dir" "/upload/$username"
    fi
done

# Clean up
rm -rf $EXTRACT_DIR

echo "Script completed successfully."
if [ -n "$USERS_TO_SKIP" ]; then
    echo "SFTP users have been restored (except: ${USERS_TO_SKIP[@]})"
else
    echo "All SFTP users have been restored"
fi
echo "Please verify the users can log in successfully."

# Restart SSH service to apply changes
systemctl restart ssh