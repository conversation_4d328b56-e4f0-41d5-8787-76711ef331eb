# SFTP Server Setup on GCP

![SFTP Server](https://img.shields.io/badge/SFTP-Server-blue)
![GCP](https://img.shields.io/badge/Platform-GCP-orange)
![Bash](https://img.shields.io/badge/Bash-Scripts-green)

This repository contains scripts to set up and manage a secure SFTP server on Google Cloud Platform (GCP). The solution provides a chrooted SFTP environment with user isolation and comprehensive user management capabilities.

## Prerequisites

- A GCP instance running a Debian-based Linux distribution (e.g., Debian, Ubuntu)
- Root or sudo access to the instance

## Features

- Secure SFTP server with chroot environment
- User isolation in separate directories
- Strong password policies and security configurations
- Support for both password and public key authentication
- Comprehensive user management tools

## Core Scripts

1. **setup_gcp_sftp.sh**
   - Main setup script that initializes the SFTP environment
   - Installs required packages and configures initial settings
   ```bash
   sudo bash setup_gcp_sftp.sh
   ```

2. **setup_sftp_server.sh**
   - Configures core SFTP server settings
   - Sets up security policies and password requirements
   - Creates necessary directories and permissions
   ```bash
   sudo bash setup_sftp_server.sh
   ```

3. **create_sftp_user.sh**
   - Creates new SFTP users with proper permissions
   - Sets up user isolation and chroot environment
   ```bash
   sudo bash create_sftp_user.sh username
   ```

## Setup Instructions

### 1. Initial Setup

```bash
# Upload scripts on the new instance from UI

# Make scripts executable
chmod +x *.sh

# Run initial setup
sudo bash setup_gcp_sftp.sh
```

### 2. Security Configuration

The setup automatically implements several security measures:

- Chrooted SFTP environment
- Strong password policies (Removed for standard) (minimum 14 characters, mixed case, numbers, and special characters)
- User isolations
- SSH hardening
- No root login allowed
- Restricted shell access for SFTP users

#### Installing and Configuring fail2ban

Install fail2ban for protection against brute force attacks:

```bash
# Install fail2ban
sudo apt-get update
sudo apt-get install -y fail2ban

# Create fail2ban configuration
sudo nano /etc/fail2ban/jail.local
```

Add the following configuration to `/etc/fail2ban/jail.local`:

```ini
[DEFAULT]
bantime = 3600
maxretry = 3
banaction = iptables-multiport

[sshd]
enabled = true

[sshd-invaliduser]
enabled = true
maxretry = 3
port = ssh
logpath = %(sshd_log)s
backend = %(sshd_backend)s
```

Restart fail2ban to apply the configuration:
```bash
sudo systemctl restart fail2ban
```

You can check fail2ban status with:
```bash
sudo fail2ban-client status
```

### 3. Adding New Users

To add a new SFTP user:

```bash
# Create new SFTP user
sudo bash create_sftp_user.sh newuser

# Set password for the new user
sudo passwd newuser
```

The script will:
- Create the user and add them to the sftp_users group
- Set up the chroot environment
- Create necessary directories with proper permissions
- Configure symbolic links

## Directory Structure

The SFTP server uses the following directory structure:

```
/data/
└── username/
    └── upload/     # User's upload directory (775 permissions)

/upload/
└── username -> /data/username  # Symbolic link for chroot
```

## Security Details

The setup implements the following security configurations:

1. **SSH Configuration**
   - Root login disabled
   - Password and public key authentication enabled
   - TCP forwarding disabled
   - X11 forwarding disabled

2. **Password Policy**
   - Minimum length: 14 characters
   - Requires: uppercase, lowercase, numbers, and special characters
   - Three failed login attempts before temporary lockout
   - For a new user password, requirments will give a warning if not met
   - You can continue with same password to bypass warning during user creation

3. **Directory Permissions**
   - Base directories (/data, /upload): 755, owned by root:root
   - User directories: 755, owned by root:sftp_users
   - Upload directories: 775, owned by user:sftp_users

## Troubleshooting

Common issues and solutions:

1. **Permission Issues**
   - Check directory ownership:
     ```bash
     ls -la /data/username
     ls -la /data/username/upload
     ```
   - Verify symbolic links:
     ```bash
     ls -la /upload/username
     ```

2. **Connection Issues**
   - Check SSH service status:
     ```bash
     systemctl status ssh
     ```
   - Review logs:
     ```bash
     tail -f /var/log/auth.log
     ```

3. **User Access Problems**
   - Verify user in sftp_users group:
     ```bash
     groups username
     ```
   - Check chroot directory structure:
     ```bash
     namei -l /upload/username
     ```

## Best Practices

1. Regularly monitor system logs for unauthorized access attempts
2. Keep the system and packages updated
3. Implement regular backup procedures
4. Test user access after creation
5. Review and audit user permissions periodically

---

📝 **Note**: Always test in a non-production environment first and ensure proper backup procedures are in place before making changes to a production system.
