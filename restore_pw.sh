#!/bin/bash

# Check if script is run as root
if [[ $EUID -ne 0 ]]; then
   echo "This script must be run as root"
   exit 1
fi

# Check if expect is installed
if ! command -v expect >/dev/null 2>&1; then
    echo "Installing expect..."
    apt-get update && apt-get install -y expect
fi

# Check if CSV file was provided
if [ $# -ne 1 ]; then
    echo "Usage: $0 <csv_file>"
    echo "Example: $0 users.csv"
    echo "CSV format should be: username,password"
    exit 1
fi

CSV_FILE=$1

# Check if CSV file exists
if [ ! -f "$CSV_FILE" ]; then
    echo "Error: CSV file '$CSV_FILE' not found"
    exit 1
fi

echo "Starting password updates from CSV file: $CSV_FILE"
echo "----------------------------------------"

# Read the CSV file line by line, skipping the header
sed 1d "$CSV_FILE" | while IFS=, read -r username password
do
    # Remove any whitespace or quotes
    username=$(echo "$username" | tr -d '"' | tr -d ' ')
    password=$(echo "$password" | tr -d '"' | tr -d ' ')
    
    # Check if user exists
    if id "$username" >/dev/null 2>&1; then
        echo "Setting password for user: $username"
        
        # Use expect to automate passwd command
        expect << EOF
spawn passwd $username
expect "New password: "
send "$password\r"
expect "Retype new password: "
send "$password\r"
expect eof
EOF
        
        if [ $? -eq 0 ]; then
            echo "Password successfully set for $username"
        else
            echo "Failed to set password for $username"
        fi
    else
        echo "User $username does not exist, skipping..."
    fi
    echo "----------------------------------------"
done

echo "Password update complete"