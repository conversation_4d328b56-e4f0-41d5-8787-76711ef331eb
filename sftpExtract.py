

# Run this query to get the credentials
# select * from job_config jc where job_config_definition_id = 2

# After the query results appear, right-click on the results grid
# Select "Export Data"
# Choose "CSV"
# Set your export path, example: /Users/<USER>/Desktop/sftpBackup/credentials.csv
# Configure any CSV options (delimiter, headers, etc.)
# Click "Proceed"
# Confirm the export
# Run the script


import json
import csv
from pathlib import Path

def clean_credentials():
    # Input and output file paths
    input_file = Path("/Users/<USER>/Desktop/sftpBackup/credentials.csv")
    output_file = Path("/Users/<USER>/Desktop/sftpBackup/clean_credentials.csv")
    
    with input_file.open('r') as infile, output_file.open('w', newline='') as outfile:
        reader = csv.DictReader(infile)
        writer = csv.writer(outfile)
        
        # Write the new simplified header
        writer.writerow(['username','password'])
        
        # Process each row
        for row in reader:
            try:
                # Parse the config_settings JSON
                config = json.loads(row['config_settings'])
                
                # Extract just username and password
                username = config.get('userName', '')
                password = config.get('password', '')
                
                # Write only username and password
                if username and password:  # Only write if both fields have values
                    writer.writerow([username, password])
                    
            except json.JSONDecodeError:
                print(f"Skipping invalid JSON in row: {row}")
            except KeyError as e:
                print(f"Missing expected column: {e}")

if __name__ == "__main__":
    clean_credentials()
