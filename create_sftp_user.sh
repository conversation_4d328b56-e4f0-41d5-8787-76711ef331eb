#!/bin/bash

# Check if script is run as root
if [[ $EUID -ne 0 ]]; then
   echo "This script must be run as root"
   exit 1
fi

# Check if username was provided
if [ $# -ne 1 ]; then
    echo "Usage: $0 <username>"
    echo "Example: $0 newuser"
    exit 1
fi

USERNAME=$1
SFTP_GROUP="sftp_users"

# Function to verify a command succeeded
verify_command() {
    if [ $? -ne 0 ]; then
        echo "Error: $1"
        exit 1
    fi
}

# Check if user already exists
if id "$USERNAME" &>/dev/null; then
    echo "User $USERNAME already exists"
    exit 1
fi

# Create user and add to sftp_users group
useradd -g $SFTP_GROUP -d /upload -s /sbin/nologin "$USERNAME"
verify_command "Failed to create user $USERNAME"

# Create the directory structure
echo "Creating directory structure for $USERNAME..."
mkdir -p "/data/$USERNAME/upload"
verify_command "Failed to create directories for $USERNAME"

# Set ownership and permissions
echo "Setting up permissions..."
chown root:$SFTP_GROUP "/data/$USERNAME"
verify_command "Failed to set ownership on /data/$USERNAME"

chmod 755 "/data/$USERNAME"
verify_command "Failed to set permissions on /data/$USERNAME"

chown "$USERNAME:$SFTP_GROUP" "/data/$USERNAME/upload"
verify_command "Failed to set ownership on /data/$USERNAME/upload"

chmod 775 "/data/$USERNAME/upload"
verify_command "Failed to set permissions on /data/$USERNAME/upload"

# Create symbolic link
echo "Creating symbolic link..."
ln -sfn "/data/$USERNAME" "/upload/$USERNAME"
verify_command "Failed to create symbolic link"

# Verify the setup
echo "Verifying setup..."
echo "Checking user and group:"
id "$USERNAME"
getent group "$SFTP_GROUP"

echo -e "\nChecking directory structure:"
ls -la "/data/$USERNAME"
ls -la "/data/$USERNAME/upload"

echo -e "\nChecking symbolic link:"
ls -la "/upload/$USERNAME"
namei -l "/upload/$USERNAME"

echo -e "\nSetup complete for user $USERNAME"
echo "Please set a password for the user with: passwd $USERNAME"